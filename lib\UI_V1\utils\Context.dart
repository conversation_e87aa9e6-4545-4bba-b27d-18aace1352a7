import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../backend/model/match.dart';
import '../../backend/rest/match_details.dart';
import 'cache_manager.dart';
import 'user_context.dart';

/// Manages application-wide data and caching.
///
/// Provides methods for fetching, caching, and retrieving match data.
class Context {
  /// Cache key for upcoming matches
  static const String _upcomingMatchesKey = 'upcomingMatches';

  /// Cache key for live matches
  static const String _liveMatchesKey = 'liveMatches';

  /// Duration for which cached data is considered valid
  static const Duration _cacheDuration = Duration(minutes: 15);

  /// Fetches and caches upcoming matches with improved caching
  static Future<void> fetchAndCacheMatches() async {
    try {
      // Check if we have valid cached data
      bool cacheExists = await CacheManager.cacheExists(_upcomingMatchesKey);

      if (!cacheExists) {
        // Fetch fresh data if cache doesn't exist or is expired
        List<Match> upcomingMatches =
            await MatchDetails.getUpcomingMatches(UserContext.userId);

        // Save to both new cache system and old system for backward compatibility
        await CacheManager.saveToCache(
          key: _upcomingMatchesKey,
          data: upcomingMatches.map((e) => e.toJson()).toList(),
          cacheDuration: _cacheDuration,
        );

        // Keep the old caching mechanism for compatibility
        final SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.setStringList('upcomingMatchesList',
            upcomingMatches.map((e) => e.toString()).toList());
      }
    } catch (e) {
      debugPrint("Error fetching and caching matches: $e");
    }
  }

  /// Fetches and caches live matches with improved caching
  static Future<void> fetchAndCacheLiveMatches() async {
    try {
      // Check if we have valid cached data
      bool cacheExists = await CacheManager.cacheExists(_liveMatchesKey);

      if (!cacheExists) {
        // Fetch fresh data if cache doesn't exist or is expired
        List<Match> fetchedList =
            await MatchDetails.getUserJoinedMatches(UserContext.userId);

        // Save to both new cache system and old system for backward compatibility
        await CacheManager.saveToCache(
          key: _liveMatchesKey,
          data: fetchedList.map((e) => e.toJson()).toList(),
          cacheDuration: _cacheDuration,
        );

        // Keep the old caching mechanism for compatibility
        final SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.setStringList('liveMatchesList',
            fetchedList.map((e) => e.toString()).toList());
      }
    } catch (e) {
      debugPrint("Error fetching and caching live matches: $e");
    }
  }

  /// Gets upcoming matches from cache if available, otherwise fetches from network
  static Future<List<Match>> getUpcomingMatches() async {
    try {
      // Try to get from new cache first
      List<Match>? cachedMatches = await CacheManager.getListFromCache<Match>(
        key: _upcomingMatchesKey,
        fromJson: (json) => Match.fromJson(json),
      );

      if (cachedMatches != null) {
        return cachedMatches;
      }

      // If not in new cache, try old cache
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      List<String>? matchesJson = prefs.getStringList('upcomingMatchesList');

      if (matchesJson != null && matchesJson.isNotEmpty) {
        return matchesJson
            .map((match) => Match.fromJson(jsonDecode(match)))
            .toList();
      }

      // If not in any cache, fetch and cache
      await fetchAndCacheMatches();

      // Try again from cache
      final List<String>? refreshedMatchesJson =
          prefs.getStringList('upcomingMatchesList');

      if (refreshedMatchesJson != null && refreshedMatchesJson.isNotEmpty) {
        return refreshedMatchesJson
            .map((match) => Match.fromJson(jsonDecode(match)))
            .toList();
      }

      return [];
    } catch (e) {
      debugPrint("Error getting upcoming matches: $e");
      return [];
    }
  }

  /// Gets live matches from cache if available, otherwise fetches from network
  static Future<List<Match>> getLiveMatches() async {
    try {
      // Try to get from new cache first
      List<Match>? cachedMatches = await CacheManager.getListFromCache<Match>(
        key: _liveMatchesKey,
        fromJson: (json) => Match.fromJson(json),
      );

      if (cachedMatches != null) {
        return cachedMatches;
      }

      // If not in new cache, try old cache
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      List<String>? matchesJson = prefs.getStringList('liveMatchesList');

      if (matchesJson != null && matchesJson.isNotEmpty) {
        return matchesJson
            .map((match) => Match.fromJson(jsonDecode(match)))
            .toList();
      }

      // If not in any cache, fetch and cache
      await fetchAndCacheLiveMatches();

      // Try again from cache
      final List<String>? refreshedMatchesJson =
          prefs.getStringList('liveMatchesList');

      if (refreshedMatchesJson != null && refreshedMatchesJson.isNotEmpty) {
        return refreshedMatchesJson
            .map((match) => Match.fromJson(jsonDecode(match)))
            .toList();
      }

      return [];
    } catch (e) {
      debugPrint("Error getting live matches: $e");
      return [];
    }
  }
}
