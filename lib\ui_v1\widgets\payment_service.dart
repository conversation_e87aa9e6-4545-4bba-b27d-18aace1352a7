import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:http/http.dart' as http;
import 'package:phonepe_payment_sdk/phonepe_payment_sdk.dart';
import '../../backend/rest/user_details.dart';
import '../../core/config/app_config.dart';
import '../../core/utils/logger.dart';

class PaymentService {
  late final String merchantId;
  late String transactionId;

  PaymentService() {
    final config = AppConfig();
    merchantId = config.getApiKey('phonepe_merchant_id') ?? '';
  }

  Future<void> startTransaction(
      double amount, Function onSuccess, Function onError) async {
    try {
      int convertedAmount = (amount * 100).toInt();

      final authToken = await fetchAuthToken();
      if (authToken == null) throw Exception("Failed to obtain auth token");

      transactionId = await UserDetails.getMerchantTransactionId();
      final orderData = await createOrder(authToken, convertedAmount);
      if (orderData == null) throw Exception("Failed to create order");

      bool isIntialized = await _initializeSdk();
      if (!isIntialized) {
        throw Exception("Could Not Initialize");
      }
      final String requestData = await _generateRequestData(authToken, orderData);
      await PhonePePaymentSdk.startTransaction(
        requestData,
        "top3.UI_V1",
      ).then((response) {
        _handleTransactionResponse(
          authToken,
          response!['status'],
          onSuccess,
          onError,
        );
      });
    } catch (error) {
      onError("Error during transaction: $error");
    }
  }


  Future<String?> fetchAuthToken() async {
    final dio = Dio();
    final config = AppConfig();
    final oauthEndpoint = config.getEndpoint('phonepe_oauth')!;
    final clientId = config.getApiKey('phonepe_client_id');
    final clientSecret = config.getSecret('phonepe_client_secret');
    try {
      final response = await dio.post(
        oauthEndpoint,
        data: {
          'client_id': clientId,
          'client_version': '1',
          'client_secret': clientSecret,
          'grant_type': 'client_credentials',
        },
        options: Options(
          contentType: Headers.formUrlEncodedContentType,
          headers: {
            'Accept': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        return data['access_token'];
      } else {
        // Use logger instead of print
        Logger.error("Auth Token Error", error: response.data);
        return null;
      }
    } on DioException catch (e) {
      if (e.response != null) {
        Logger.error("PhonePe API Error",
          error: "Status: ${e.response?.statusCode}, Data: ${e.response?.data}");
      } else {
        Logger.error("PhonePe Network Error", error: e.message);
      }
      return null;
    }
  }

  Future<Map<String, dynamic>?> createOrder(
      String accessToken, int amount) async {
    // Get the order endpoint from config or use default
    final config = AppConfig();
    final orderEndpoint = config.getEndpoint('phonepe_order');

    final url = Uri.parse(orderEndpoint!);

    final requestBody = {
      "merchantOrderId": transactionId,
      "amount": amount,
      "paymentFlow": {"type": "PG_CHECKOUT"}
    };

    try {
      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'O-Bearer $accessToken',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        Logger.error("Create Order Error", error: response.body);
        return null;
      }
    } catch (e) {
      Logger.error("Create Order Exception", error: e);
      return null;
    }
  }

  Future<bool> _initializeSdk() async {
    String environmentValue = 'SANDBOX';// (SANDBOX/PROD)
    return await PhonePePaymentSdk.init(
      environmentValue,
      merchantId,
      transactionId,
      true,
    );
  }

  Future<String> _generateRequestData(String authToken,
      Map<String, dynamic> orderData) async {
    Map<String, dynamic> payload = {
      "orderId": orderData["orderId"],
      "merchantId": merchantId,
      "token": orderData["token"],
      "paymentMode": {"type": "PAY_PAGE"},
    };
    return jsonEncode(payload);
  }

  Future<void> _handleTransactionResponse(String authToken, String status, Function onSuccess, Function onError) async {
    try {
      await updateStatus(authToken);
      if (status == "SUCCESS") {
        onSuccess("Transaction Successful");
      } else if (status == "FAILURE") {
        onError("Transaction Failed!");
      }
    } catch (e) {
      onError("Status check error: $e");
    }
  }

  Future<void> updateStatus(String authToken) async {
    try {
      final config = AppConfig();
      final statusEndpoint = config.getEndpoint('phonepe_status')!;
      final statusUrl = '$statusEndpoint/$transactionId/status';

      final url = Uri.parse(statusUrl);

      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'O-Bearer $authToken',
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> res = jsonDecode(response.body);
        await UserDetails.saveTransaction(jsonEncode(res));
        Logger.info("Transaction status updated successfully", error: res);
      } else {
        Logger.error("Failed to retrieve status",
            error: "Status Code: ${response.statusCode}, Body: ${response.body}");
        throw HttpException(
            "Failed to retrieve status, Status Code: ${response.statusCode}");
      }
    } catch (e) {
      Logger.error("Status retrieval failed", error: e);
      throw HttpException("Status retrieval failed: $e");
    }
  }
}
