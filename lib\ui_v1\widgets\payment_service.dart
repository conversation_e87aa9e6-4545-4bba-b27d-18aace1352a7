import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:http/http.dart' as http;
import 'package:phonepe_payment_sdk/phonepe_payment_sdk.dart';
import '../../backend/rest/user_details.dart';
import '../../core/config/app_config.dart';
import '../../core/error/exceptions.dart';
import '../../core/utils/logger.dart';

/// Callback function type for successful payment completion
typedef PaymentSuccessCallback = void Function();

/// Callback function type for payment errors
typedef PaymentErrorCallback = void Function(String errorMessage);

/// Service class for handling PhonePe payment integration
///
/// Provides methods for:
/// - Authentication with PhonePe API
/// - Order creation and management
/// - Payment processing
/// - Transaction status tracking
class PaymentService {
  // Constants
  static const String _packageName = 'top3.UI_V1';
  static const String _clientVersion = '1';
  static const String _grantType = 'client_credentials';
  static const String _paymentFlowType = 'PG_CHECKOUT';
  static const String _paymentModeType = 'PAY_PAGE';
  static const String _environment = 'SANDBOX'; // Change to 'PROD' for production
  static const Duration _requestTimeout = Duration(seconds: 30);
  static const int _amountMultiplier = 100; // Convert rupees to paise

  // Status constants
  static const String _statusSuccess = 'SUCCESS';
  static const String _statusFailure = 'FAILURE';

  // Singleton pattern
  static PaymentService? _instance;
  static PaymentService get instance => _instance ??= PaymentService._internal();

  // Private constructor
  PaymentService._internal() {
    _initializeService();
  }

  // Factory constructor for backward compatibility
  factory PaymentService() => instance;

  // Instance variables
  late final String _merchantId;
  late final AppConfig _config;
  late final Dio _dio;
  late final http.Client _httpClient;
  String? _currentTransactionId;

  /// Initialize the payment service with configuration
  void _initializeService() {
    _config = AppConfig();
    _merchantId = _config.getApiKey('phonepe_merchant_id') ?? '';

    if (_merchantId.isEmpty) {
      throw ValidationException('PhonePe merchant ID not configured');
    }

    // Initialize HTTP clients with proper configuration
    _dio = Dio(BaseOptions(
      connectTimeout: _requestTimeout,
      receiveTimeout: _requestTimeout,
      sendTimeout: _requestTimeout,
    ));

    _httpClient = http.Client();

    Logger.info('PaymentService initialized successfully');
  }

  /// Get the current merchant ID
  String get merchantId => _merchantId;

  /// Get the current transaction ID
  String? get currentTransactionId => _currentTransactionId;

  /// Dispose resources when service is no longer needed
  void dispose() {
    _dio.close();
    _httpClient.close();
    Logger.info('PaymentService resources disposed');
  }

  /// Start a payment transaction
  ///
  /// [amount] - Amount in rupees (will be converted to paise)
  /// [onSuccess] - Callback for successful transaction
  /// [onError] - Callback for transaction errors
  Future<void> startTransaction(
    double amount,
    PaymentSuccessCallback onSuccess,
    PaymentErrorCallback onError,
  ) async {
    try {
      // Validate input
      if (amount <= 0) {
        throw ValidationException('Amount must be greater than zero');
      }

      final int convertedAmount = (amount * _amountMultiplier).toInt();
      Logger.info('Starting transaction for amount: ₹$amount ($convertedAmount paise)');

      // Step 1: Get authentication token
      final authToken = await fetchAuthToken();
      if (authToken == null) {
        throw AuthException('Failed to obtain authentication token');
      }

      // Step 2: Get transaction ID
      _currentTransactionId = await UserDetails.getMerchantTransactionId();
      Logger.info('Generated transaction ID: $_currentTransactionId');

      // Step 3: Create order
      final orderData = await createOrder(authToken, convertedAmount);
      if (orderData == null) {
        throw ServerException('Failed to create order');
      }

      // Step 4: Initialize SDK
      final isInitialized = await _initializeSdk();
      if (!isInitialized) {
        throw ServerException('Failed to initialize PhonePe SDK');
      }

      // Step 5: Generate request data and start transaction
      final requestData = await _generateRequestData(orderData);
      final response = await PhonePePaymentSdk.startTransaction(
        requestData,
        _packageName,
      );

      // Step 6: Handle response
      await _handleTransactionResponse(
        authToken,
        response?['status'] ?? _statusFailure,
        onSuccess,
        onError,
      );

    } on AppException catch (e) {
      Logger.error('Payment transaction failed', error: e);
      onError(e.message);
    } catch (error) {
      Logger.error('Unexpected error during transaction', error: error);
      onError('Transaction failed: ${error.toString()}');
    }
  }


  /// Fetch authentication token from PhonePe OAuth API
  ///
  /// Returns the access token or null if authentication fails
  Future<String?> fetchAuthToken() async {
    try {
      final oauthEndpoint = _config.getEndpoint('phonepe_oauth');
      if (oauthEndpoint == null) {
        throw ValidationException('PhonePe OAuth endpoint not configured');
      }

      final clientId = _config.getApiKey('phonepe_client_id');
      final clientSecret = _config.getSecret('phonepe_client_secret');

      if (clientId == null || clientSecret == null) {
        throw ValidationException('PhonePe client credentials not configured');
      }

      Logger.info('Requesting authentication token from PhonePe');

      final response = await _dio.post(
        oauthEndpoint,
        data: {
          'client_id': clientId,
          'client_version': _clientVersion,
          'client_secret': clientSecret,
          'grant_type': _grantType,
        },
        options: Options(
          contentType: Headers.formUrlEncodedContentType,
          headers: {
            'Accept': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final accessToken = data['access_token'] as String?;

        if (accessToken == null || accessToken.isEmpty) {
          throw ServerException('Invalid access token received from PhonePe');
        }

        Logger.info('Authentication token obtained successfully');
        return accessToken;
      } else {
        Logger.error('Auth token request failed',
            error: 'Status: ${response.statusCode}, Data: ${response.data}');
        throw ApiException(
          'Authentication failed',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.response != null) {
        Logger.error('PhonePe API error during authentication',
            error: 'Status: ${e.response?.statusCode}, Data: ${e.response?.data}');
        throw ApiException(
          'PhonePe API error: ${e.response?.statusCode}',
          statusCode: e.response?.statusCode,
        );
      } else {
        Logger.error('Network error during authentication', error: e.message);
        throw NetworkException('Network error during authentication: ${e.message}');
      }
    } on AppException {
      rethrow;
    } catch (e) {
      Logger.error('Unexpected error during authentication', error: e);
      throw ServerException('Authentication failed: ${e.toString()}');
    }
  }

  /// Create a payment order with PhonePe
  ///
  /// [accessToken] - OAuth access token
  /// [amount] - Amount in paise
  /// Returns order data or null if creation fails
  Future<Map<String, dynamic>?> createOrder(String accessToken, int amount) async {
    try {
      if (_currentTransactionId == null) {
        throw ValidationException('Transaction ID not available');
      }

      final orderEndpoint = _config.getEndpoint('phonepe_order');
      if (orderEndpoint == null) {
        throw ValidationException('PhonePe order endpoint not configured');
      }

      final requestBody = {
        "merchantOrderId": _currentTransactionId!,
        "amount": amount,
        "paymentFlow": {"type": _paymentFlowType}
      };

      Logger.info('Creating order for transaction: $_currentTransactionId, amount: $amount paise');

      final response = await _httpClient.post(
        Uri.parse(orderEndpoint),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'O-Bearer $accessToken',
        },
        body: jsonEncode(requestBody),
      ).timeout(_requestTimeout);

      if (response.statusCode == 200) {
        final orderData = jsonDecode(response.body) as Map<String, dynamic>;
        Logger.info('Order created successfully');
        return orderData;
      } else {
        Logger.error('Order creation failed',
            error: 'Status: ${response.statusCode}, Body: ${response.body}');
        throw ApiException(
          'Order creation failed',
          statusCode: response.statusCode,
        );
      }
    } on SocketException catch (e) {
      Logger.error('Network error during order creation', error: e);
      throw NetworkException('Network error during order creation: ${e.message}');
    } on TimeoutException catch (e) {
      Logger.error('Timeout during order creation', error: e);
      throw NetworkException('Order creation timed out');
    } on FormatException catch (e) {
      Logger.error('Invalid response format during order creation', error: e);
      throw JsonParseException('Invalid order response format');
    } on AppException {
      rethrow;
    } catch (e) {
      Logger.error('Unexpected error during order creation', error: e);
      throw ServerException('Order creation failed: ${e.toString()}');
    }
  }

  /// Initialize PhonePe SDK
  ///
  /// Returns true if initialization is successful
  Future<bool> _initializeSdk() async {
    try {
      if (_currentTransactionId == null) {
        throw ValidationException('Transaction ID not available for SDK initialization');
      }

      Logger.info('Initializing PhonePe SDK');

      final isInitialized = await PhonePePaymentSdk.init(
        _environment,
        _merchantId,
        _currentTransactionId!,
        true,
      );

      if (isInitialized) {
        Logger.info('PhonePe SDK initialized successfully');
      } else {
        Logger.error('PhonePe SDK initialization failed');
      }

      return isInitialized;
    } catch (e) {
      Logger.error('Error during PhonePe SDK initialization', error: e);
      return false;
    }
  }

  /// Generate request data for PhonePe SDK
  ///
  /// [orderData] - Order data from createOrder response
  /// Returns JSON encoded request data
  Future<String> _generateRequestData(Map<String, dynamic> orderData) async {
    try {
      final payload = {
        "orderId": orderData["orderId"],
        "merchantId": _merchantId,
        "token": orderData["token"],
        "paymentMode": {"type": _paymentModeType},
      };

      final requestData = jsonEncode(payload);
      Logger.info('Generated request data for PhonePe SDK');
      return requestData;
    } catch (e) {
      Logger.error('Error generating request data', error: e);
      throw JsonParseException('Failed to generate request data: ${e.toString()}');
    }
  }

  /// Handle transaction response from PhonePe SDK
  ///
  /// [authToken] - OAuth access token
  /// [status] - Transaction status from PhonePe
  /// [onSuccess] - Success callback
  /// [onError] - Error callback
  Future<void> _handleTransactionResponse(
    String authToken,
    String status,
    PaymentSuccessCallback onSuccess,
    PaymentErrorCallback onError,
  ) async {
    try {
      Logger.info('Handling transaction response with status: $status');

      // Update transaction status
      await updateStatus(authToken);

      if (status == _statusSuccess) {
        Logger.info('Transaction completed successfully');
        onSuccess();
      } else if (status == _statusFailure) {
        Logger.warn('Transaction failed with status: $status');
        onError('Transaction failed');
      } else {
        Logger.warn('Unknown transaction status: $status');
        onError('Transaction status unknown: $status');
      }
    } on AppException catch (e) {
      Logger.error('Error during transaction status handling', error: e);
      onError('Status check error: ${e.message}');
    } catch (e) {
      Logger.error('Unexpected error during transaction status handling', error: e);
      onError('Status check error: ${e.toString()}');
    }
  }

  /// Update transaction status from PhonePe API
  ///
  /// [authToken] - OAuth access token
  /// Fetches the latest transaction status and saves it
  Future<void> updateStatus(String authToken) async {
    try {
      if (_currentTransactionId == null) {
        throw ValidationException('Transaction ID not available for status update');
      }

      final statusEndpoint = _config.getEndpoint('phonepe_status');
      if (statusEndpoint == null) {
        throw ValidationException('PhonePe status endpoint not configured');
      }

      // Construct status URL with details parameter
      final statusUrl = '$statusEndpoint/$_currentTransactionId/status?details=true';

      Logger.info('Fetching transaction status for: $_currentTransactionId');

      final response = await _httpClient.get(
        Uri.parse(statusUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'O-Bearer $authToken',
        },
      ).timeout(_requestTimeout);

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body) as Map<String, dynamic>;

        // Save transaction details
        await UserDetails.saveTransaction(jsonEncode(responseData));

        Logger.info('Transaction status updated successfully',
            error: 'Status: ${responseData['state']}, Amount: ${responseData['amount']}');
      } else {
        Logger.error('Failed to retrieve transaction status',
            error: 'Status Code: ${response.statusCode}, Body: ${response.body}');
        throw ApiException(
          'Failed to retrieve transaction status',
          statusCode: response.statusCode,
        );
      }
    } on SocketException catch (e) {
      Logger.error('Network error during status update', error: e);
      throw NetworkException('Network error during status update: ${e.message}');
    } on TimeoutException catch (e) {
      Logger.error('Timeout during status update', error: e);
      throw NetworkException('Status update timed out');
    } on FormatException catch (e) {
      Logger.error('Invalid response format during status update', error: e);
      throw JsonParseException('Invalid status response format');
    } on AppException {
      rethrow;
    } catch (e) {
      Logger.error('Unexpected error during status update', error: e);
      throw ServerException('Status update failed: ${e.toString()}');
    }
  }
}
}
