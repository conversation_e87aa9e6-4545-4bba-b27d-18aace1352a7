import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';
import 'package:phonepe_payment_sdk/phonepe_payment_sdk.dart';
import '../../backend/rest/user_details.dart';
import '../../core/config/app_config.dart';
import '../../core/utils/logger.dart';

class PaymentService {
  late final merchantId;
  late String transactionId;

  PaymentService() {
    final config = AppConfig();
    merchantId = config.getApiKey('phonepe_merchant_id');
  }

  Future<void> startTransaction(
      double amount, Function onSuccess, Function onError) async {
    try {
      int convertedAmount = (amount * 100).toInt();
      
      final authToken = await fetchAuthToken();
      if (authToken == null) throw Exception("Failed to obtain auth token");

      transactionId = await UserDetails.getMerchantTransactionId();
      final orderData = await createOrder(authToken, convertedAmount);
      if (orderData == null) throw Exception("Failed to create order");

      bool isIntialized = await _initializeSdk();
      if (!isIntialized) {
        throw Exception("Could Not Initialize");
      }
      final String requestData = await _generateRequestData(authToken, orderData);
      await PhonePePaymentSdk.startTransaction(
        requestData,
        "top3.UI_V1",
      ).then((response) {
        _handleTransactionResponse(
          authToken,
          response!['status'],
          onSuccess,
          onError,
        );
      });
    } catch (error) {
      onError("Error during transaction: $error");
    }
  }

  
  Future<String?> fetchAuthToken() async {
    final dio = Dio();
    final config = AppConfig();
    final oauthEndpoint = config.getEndpoint('phonepe_oauth')!;
    final clientId = config.getApiKey('phonepe_client_id');
    final clientSecret = config.getSecret('phonepe_client_secret');
    try {
      final response = await dio.post(
        oauthEndpoint,
        data: {
          'client_id': clientId,
          'client_version': '1',
          'client_secret': clientSecret,
          'grant_type': 'client_credentials',
        },
        options: Options(
          contentType: Headers.formUrlEncodedContentType,
          headers: {
            'Accept': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        return data['access_token'];
      } else {
        // Use logger instead of print
        Logger.error("Auth Token Error", error: response.data);
        return null;
      }
    } on DioException catch (e) {
      if (e.response != null) {
        Logger.error("PhonePe API Error",
          error: "Status: ${e.response?.statusCode}, Data: ${e.response?.data}");
      } else {
        Logger.error("PhonePe Network Error", error: e.message);
      }
      return null;
    }
  }

  Future<Map<String, dynamic>?> createOrder(
      String accessToken, int amount) async {
    // Get the order endpoint from config or use default
    final config = AppConfig();
    final orderEndpoint = config.getEndpoint('phonepe_order');

    final url = Uri.parse(orderEndpoint!);

    final requestBody = {
      "merchantOrderId": transactionId,
      "amount": amount,
      "paymentFlow": {"type": "PG_CHECKOUT"}
    };

    try {
      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'O-Bearer $accessToken',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        Logger.error("Create Order Error", error: response.body);
        return null;
      }
    } catch (e) {
      Logger.error("Create Order Exception", error: e);
      return null;
    }
  }

  Future<bool> _initializeSdk() async {
    String environmentValue = 'SANDBOX';// (SANDBOX/PROD)
    return await PhonePePaymentSdk.init(
      environmentValue,
      merchantId,
      transactionId,
      true,
    );
  }

  Future<String> _generateRequestData(String authToken,
      Map<String, dynamic> orderData) async {
    Map<String, dynamic> payload = {
      "orderId": orderData["orderId"],
      "merchantId": merchantId,
      "token": orderData["token"],
      "paymentMode": {"type": "PAY_PAGE"},
    };
    return jsonEncode(payload);
  }

  Future<void> _handleTransactionResponse(String authToken, String status, Function onSuccess, Function onError) async {
    try {
      await updateStatus(authToken);
      if (status == "SUCCESS") {
        onSuccess("Transaction Successful");
      } else if (status == "FAILURE") {
        onError("Transaction Failed!");
      }
    } catch (e) {
      onError("Status check error: $e");
    }
  }

  Future<void> updateStatus(String authToken) async {
    try {
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> res = jsonDecode(response.body);
        await UserDetails.saveTransaction(jsonEncode(res));
      } else {
        throw HttpException(
            "Failed to retrieve status, Status Code: ${response.statusCode}");
      }
    } catch (e) {
      throw HttpException("Status retrieval failed: $e");
    }
  }
}


Complete updateStatus() by curl --location 'https://api-preprod.phonepe.com/apis/pg-sandbox/checkout/v2/order/{transactionId}/status?details=true' --header 'Content-Type: application/json' --header 'Authorization: O-Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHBpcmVzT24iOjE3NDgxODQ4MjgyNDAsIm1lcmNoYW50SWQiOiJURVNULU0yMkZUVE1FS1YyOEwiLCJtZXJjaGFudE9yZGVySWQiOiIxNzQ4MTYxNDI3OTk5SFJyTE1pQkdFbVNibDlIUURhd0V4YWJNaGJxMSJ9.xxrFX1_MmoU7aG38oGJ6_NwzmQH_a36sx1KjS2-_2B4' || use authtoken and transactionId vars   and here is sample response  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed
100   553    0   553    0     0   7451      0 --:--:-- --:--:-- --:--:--  7900HTTP/1.1 200 OK
Date: Sun, 25 May 2025 09:11:26 GMT
Content-Type: application/json
Transfer-Encoding: chunked
Connection: keep-alive
x-requested-method: GET
access-control-allow-origin: *
access-control-allow-headers: Cache-Control,If-Modified-Since,Pragma,Content-Type,Authorization,X-Requested-With,Content-Length,Accept,Origin, X-VERIFY, X-MERCHANT-ID, X-CALLBACK-URL, checkouttype, x-app-package-name, x-request-env, x-auth-token, x-browser-fingerprint, x-client, x-client-major-version, x-client-name, x-client-subtype, x-client-type, x-client-version, x-device-type, x-encrypted, x-merchant-domain, x-source, x-source-channel, x-source-channel-version, x-source-integration-mode
access-control-allow-methods: OPTIONS,GET,PUT,POST,DELETE,HEAD
x-response-backend-time: *************
x-request-backend-time: *************
x-frame-options: DENY
x-content-type-options: nosniff
x-xss-protection: 1; mode=block
strict-transport-security: max-age=0; includeSubDomains; preload
cf-cache-status: DYNAMIC
vary: accept-encoding
Server: cloudflare
CF-RAY: 9453e7c7bfc03c0c-BLR

{"orderId":"OMO2505251417136955219119","state":"COMPLETED","amount":20000,"expireAt":*************,"paymentDetails":[{"paymentMode":"NET_BANKING","transactionId":"OM2505251417136965219566","timestamp":*************,"amount":20000,"state":"COMPLETED","splitInstruments":[{"amount":20000,"rail":{"type":"PG","transactionId":"*********","authorizationCode":"<authorizationCode>","serviceTransactionId":"PG2407031513566974223897"},"instrument":{"type":"NET_BANKING","bankTransactionId":"<bankTransactionId>","bankId":"ICIC","arn":"<arn>","brn":"<brn>"}}]}]}

