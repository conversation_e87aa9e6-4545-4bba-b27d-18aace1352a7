import 'dart:convert';

import 'package:http/http.dart';

import '../../core/error/exceptions.dart';
import '../../core/utils/logger.dart';
import '../../ui_v1/utils/user_context.dart';
import 'api_service.dart';

/// Handles user-related API operations
class UserDetails {
  static const String apiUrl =
      'https://t7l5b84msi.execute-api.ap-south-1.amazonaws.com/user-details';

  /// Register a new user with the API
  static Future<void> registerUser(
      String uid, String email, String name, String photoUrl) async {
    try {
      await ApiService.postRequest(apiUrl, 'registerUser', {
        'userId': uid,
        'name': name,
        'email': email,
        'photoUrl': photoUrl,
      });
    } catch (e) {
      Logger.error('Failed to register user', error: e);
      if (e is AppException) {
        rethrow;
      }
      throw ServerException('Failed to register user: ${e.toString()}');
    }
  }

  /// Get user details from the API
  static Future<Map<String, dynamic>> getUserDetails(String uid) async {
    try {
      final response = await getUserResponse(uid);

      // Safely decode the JSON response
      final dynamic decoded = json.decode(response.body);

      // Ensure we have a Map<String, dynamic>
      if (decoded is Map<String, dynamic>) {
        return decoded;
      } else if (decoded is Map) {
        // Convert to Map<String, dynamic>
        return Map<String, dynamic>.from(decoded);
      } else {
        // Return a default map with balance if the response is not a map
        Logger.warn('User details response is not a map: ${response.body}');
        return {'balance': '0'};
      }
    } on FormatException catch (e) {
      Logger.error('Failed to parse user details', error: e);
      // Return a default map instead of throwing
      return {'balance': '0'};
    } catch (e) {
      Logger.error('Failed to get user details', error: e);
      if (e is AppException) {
        // Return a default map instead of rethrowing
        return {'balance': '0'};
      }
      // Return a default map instead of throwing
      return {'balance': '0'};
    }
  }

  /// Check if a user exists in the system
  static Future<bool> userExists(String uid) async {
    try {
      final response = await getUserResponse(uid);
      return response.body != "\"OK\"";
    } catch (e) {
      Logger.error('Failed to check if user exists', error: e);
      if (e is AppException) {
        rethrow;
      }
      throw ServerException('Failed to check if user exists: ${e.toString()}');
    }
  }

  /// Get user response from the API
  static Future<Response> getUserResponse(String uid) {
    if (uid.isEmpty) {
      throw ValidationException('User ID cannot be empty');
    }

    return ApiService.postRequest(
        apiUrl, 'getUserDetailsById', {'userId': uid});
  }

  // static Future<void> updateUserBalance(
  //     String uid, String amount, String mode) async {
  //   await ApiService.postRequest(apiUrl, 'updateUserBalance',
  //       {'userId': uid, 'amount': amount, 'mode': mode});
  // }

  /// Search for users by name or email
  static Future<List<Map<String, dynamic>>> searchUsers(String search) async {
    try {
      if (search.isEmpty) {
        return [];
      }

      final response =
          await ApiService.postRequest(apiUrl, 'searchUsers', {'search': search});
      return List<Map<String, dynamic>>.from(json.decode(response.body));
    } on FormatException catch (e) {
      Logger.error('Failed to parse search results', error: e);
      throw JsonParseException('Invalid search results format');
    } catch (e) {
      Logger.error('Failed to search users', error: e);
      if (e is AppException) {
        rethrow;
      }
      throw ServerException('Failed to search users: ${e.toString()}');
    }
  }

  // static Future<Map<String, dynamic>> getFollowing(String userId) async {
  //   final response = await ApiService.postRequest(
  //       apiUrl, 'getFollowing', {'userId': userId});
  //   return jsonDecode(response.body);
  // }

  // static Future<Map<String, dynamic>> getFollowers(String userId) async {
  //   final response = await ApiService.postRequest(
  //       apiUrl, 'getFollowers', {'userId': userId});
  //   return jsonDecode(response.body);
  // }

  // static Future<bool> getFollowingStatus(String userId1, String userId2) async {
  //   final response = await ApiService.postRequest(
  //       apiUrl, 'getFollowingStatus', {'userId1': userId1, 'userId2': userId2});
  //   return response.body.contains("true");
  // }

  // static Future<void> updateFollowing(String userId1, String userId2) async {
  //   await ApiService.postRequest(
  //       apiUrl, 'updateFollowing', {'userId1': userId1, 'userId2': userId2});
  // }

  /// Save a transaction to the user's account
  static Future<void> saveTransaction(String jsonStringWithQuotes) async {
    try {
      if (UserContext.userId.isEmpty) {
        throw ValidationException('User ID is not available');
      }

      await ApiService.postRequest(apiUrl, 'saveTransaction',
          {'userId': UserContext.userId, 'paymentDetails': jsonStringWithQuotes});
    } catch (e) {
      Logger.error('Failed to save transaction', error: e);
      if (e is AppException) {
        rethrow;
      }
      throw ServerException('Failed to save transaction: ${e.toString()}');
    }
  }

  /// Get a unique merchant transaction ID for payment processing
  static Future<String> getMerchantTransactionId() async {
    try {
      if (UserContext.userId.isEmpty) {
        throw ValidationException('User ID is not available');
      }

      final response = await ApiService.postRequest(
          apiUrl, 'getMerchantTransactionId', {'userId': UserContext.userId});
      return jsonDecode(response.body);
    } on FormatException catch (e) {
      Logger.error('Failed to parse transaction ID', error: e);
      throw JsonParseException('Invalid transaction ID format');
    } catch (e) {
      Logger.error('Failed to get merchant transaction ID', error: e);
      if (e is AppException) {
        rethrow;
      }
      throw ServerException('Failed to get transaction ID: ${e.toString()}');
    }
  }

  // static Future<List<BankTransaction>> getAllTransactionDetails() async {
  //   final response = await ApiService.postRequest(
  //       apiUrl, 'getAllTransactionDetails', {'userId': UserContext.userId});
  //   final List<dynamic> jsonList = jsonDecode(response.body);
  //   return jsonList
  //       .map((json) => BankTransaction.fromJson(json as Map<String, dynamic>))
  //       .toList();
  // }

  // static Future<List<BaseTransaction>> getAllTransactions(
  //     String userId, int page) async {
  //   final response = await ApiService.postRequest(
  //       apiUrl, 'getAllTransactions', {'userId': userId, 'page': page});
  //   return (jsonDecode(response.body) as List<dynamic>)
  //       .map<BaseTransaction>((json) => json.containsKey('paymentDetails')
  //           ? BankTransaction.fromJson(json)
  //           : GameTransaction.fromJson(json))
  //       .toList();
  // }
}
